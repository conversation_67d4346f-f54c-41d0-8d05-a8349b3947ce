<template>
  <div class="overflow-hidden">
    <!-- Modern Hero Section with Bento Grid and 3D Elements -->
    <section
      ref="heroSection"
      class="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-white to-primary-50/20 overflow-hidden"
    >
      <!-- 3D Animated Background Elements -->
      <div class="absolute inset-0 overflow-hidden">
        <!-- Floating 3D orbs with gradient -->
        <div
          class="absolute top-20 left-10 w-72 h-72 bg-gradient-to-br from-primary-400/20 to-secondary-400/20 rounded-full blur-3xl animate-float"
        ></div>
        <div
          class="absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-br from-secondary-400/20 to-primary-400/20 rounded-full blur-3xl animate-float-delayed"
        ></div>
        <div
          class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[40rem] h-[40rem] bg-gradient-to-br from-primary-300/10 to-secondary-300/10 rounded-full blur-3xl"
        ></div>

        <!-- Grid pattern overlay -->
        <div class="absolute inset-0 bg-grid-pattern opacity-[0.02]"></div>
      </div>

      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 relative z-10">
        <!-- Main Hero Content with Bold Typography -->
        <div class="text-center mb-16" data-aos="fade-up">
          <!-- Modern Badge -->
          <Badge variant="success" class="mb-6 inline-flex">
            <Sparkles class="w-3 h-3 mr-1" />
            iGEM 2025 Competition
          </Badge>

          <!-- Bold Hero Title with Gradient -->
          <h1 class="text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-black mb-6 tracking-tight">
            <span class="block text-slate-900">Breaking Down The</span>
            <span
              class="block text-transparent bg-clip-text bg-gradient-to-r from-primary-600 via-primary-500 to-secondary-600 animate-gradient"
            >
              Unbreakable
            </span>
          </h1>

          <!-- Minimal Subtitle -->
          <p
            class="max-w-3xl mx-auto text-xl sm:text-2xl text-slate-600 mb-12 font-light leading-relaxed"
          >
            Innovative biological solutions powered by synthetic biology to remediate PFAS
            contamination
          </p>

          <!-- Modern CTA Buttons -->
          <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              as="router-link"
              to="/project/overview"
              size="xl"
              variant="default"
              class="group"
            >
              <span>Discover Our Solution</span>
              <ArrowRight class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
            </Button>

            <Button as="a" href="#features" size="xl" variant="outline" class="group">
              <span>See How It Works</span>
              <ChevronDown class="w-5 h-5 ml-2 group-hover:translate-y-1 transition-transform" />
            </Button>
          </div>
        </div>

        <!-- Hero Stats - Enhanced Design with Better Visual Hierarchy -->
        <div class="mt-20 max-w-5xl mx-auto" data-aos="fade-up" data-aos-delay="200">
          <!-- Stats Container with Improved Layout -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12">
            <!-- PFAS Compounds Card -->
            <div
              class="relative bg-white/70 backdrop-blur-md border border-white/40 rounded-3xl p-8 text-center hover:bg-white/80 hover:shadow-2xl hover:shadow-primary-500/10 transition-all duration-500 group transform hover:-translate-y-2"
            >
              <!-- Gradient Border Effect -->
              <div
                class="absolute inset-0 bg-gradient-to-br from-primary-500/20 via-transparent to-primary-600/20 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
              ></div>

              <!-- Icon Container with Enhanced Design -->
              <div
                class="relative w-16 h-16 bg-gradient-to-br from-primary-100 to-primary-200 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-lg"
              >
                <Beaker class="w-8 h-8 text-primary-600" />
                <!-- Subtle glow effect -->
                <div
                  class="absolute inset-0 bg-primary-400/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                ></div>
              </div>

              <!-- Number with Enhanced Typography -->
              <h3 class="text-3xl lg:text-4xl font-black text-slate-800 mb-2 tracking-tight">
                <AnimatedNumber :value="4000" suffix="+" :duration="2500" />
              </h3>

              <!-- Label with Better Spacing -->
              <p class="text-slate-600 font-medium tracking-wide">PFAS Compounds</p>

              <!-- Subtle accent line -->
              <div
                class="w-12 h-1 bg-gradient-to-r from-primary-400 to-primary-600 rounded-full mx-auto mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
              ></div>
            </div>

            <!-- Population Affected Card -->
            <div
              class="relative bg-white/70 backdrop-blur-md border border-white/40 rounded-3xl p-8 text-center hover:bg-white/80 hover:shadow-2xl hover:shadow-secondary-500/10 transition-all duration-500 group transform hover:-translate-y-2"
            >
              <!-- Gradient Border Effect -->
              <div
                class="absolute inset-0 bg-gradient-to-br from-secondary-500/20 via-transparent to-secondary-600/20 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
              ></div>

              <!-- Icon Container with Enhanced Design -->
              <div
                class="relative w-16 h-16 bg-gradient-to-br from-secondary-100 to-secondary-200 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-lg"
              >
                <Users class="w-8 h-8 text-secondary-600" />
                <!-- Subtle glow effect -->
                <div
                  class="absolute inset-0 bg-secondary-400/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                ></div>
              </div>

              <!-- Number with Enhanced Typography -->
              <h3 class="text-3xl lg:text-4xl font-black text-slate-800 mb-2 tracking-tight">
                <AnimatedNumber :value="97" suffix="%" :duration="2000" />
              </h3>

              <!-- Label with Better Spacing -->
              <p class="text-slate-600 font-medium tracking-wide">Population Affected</p>

              <!-- Subtle accent line -->
              <div
                class="w-12 h-1 bg-gradient-to-r from-secondary-400 to-secondary-600 rounded-full mx-auto mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
              ></div>
            </div>

            <!-- Biological Solution Card -->
            <div
              class="relative bg-white/70 backdrop-blur-md border border-white/40 rounded-3xl p-8 text-center hover:bg-white/80 hover:shadow-2xl hover:shadow-green-500/10 transition-all duration-500 group transform hover:-translate-y-2"
            >
              <!-- Gradient Border Effect -->
              <div
                class="absolute inset-0 bg-gradient-to-br from-green-500/20 via-transparent to-green-600/20 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
              ></div>

              <!-- Icon Container with Enhanced Design -->
              <div
                class="relative w-16 h-16 bg-gradient-to-br from-green-100 to-green-200 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-lg"
              >
                <Leaf class="w-8 h-8 text-green-600" />
                <!-- Subtle glow effect -->
                <div
                  class="absolute inset-0 bg-green-400/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                ></div>
              </div>

              <!-- Number with Enhanced Typography -->
              <h3 class="text-3xl lg:text-4xl font-black text-slate-800 mb-2 tracking-tight">
                <AnimatedNumber :value="100" suffix="%" :duration="2300" />
              </h3>

              <!-- Label with Better Spacing -->
              <p class="text-slate-600 font-medium tracking-wide">Biological Solution</p>

              <!-- Subtle accent line -->
              <div
                class="w-12 h-1 bg-gradient-to-r from-green-400 to-green-600 rounded-full mx-auto mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section with Modern Bento Grid -->
    <section id="features" class="py-24 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Section Header -->
        <div class="text-center mb-16" data-aos="fade-up">
          <Badge variant="outline" class="mb-4">
            <Microscope class="w-3 h-3 mr-1" />
            The Science
          </Badge>
          <h2 class="text-4xl sm:text-5xl font-bold text-slate-900 mb-4">Understanding PFAS</h2>
          <p class="text-xl text-slate-600 max-w-3xl mx-auto">
            Per- and polyfluoroalkyl substances (PFAS) are synthetic chemicals that persist in the
            environment
          </p>
        </div>

        <!-- Bento Grid Layout -->
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6" data-aos="fade-up" data-aos-delay="100">
          <!-- Large Feature Card -->
          <Card
            class="lg:col-span-2 lg:row-span-2 bg-gradient-to-br from-primary-50 to-white border-primary-100 overflow-hidden group"
          >
            <CardContent class="p-8">
              <div>
                <div
                  class="w-14 h-14 bg-white rounded-2xl flex items-center justify-center shadow-lg mb-4"
                >
                  <FlaskRound class="w-7 h-7 text-primary-600" />
                </div>
                <h3 class="text-2xl font-bold text-slate-900 mb-3">What are PFAS?</h3>
                <p class="text-slate-600 leading-relaxed">
                  PFAS are a group of manufactured chemicals used in industry worldwide since the
                  1940s. They're found in non-stick cookware, water-resistant clothing, food
                  packaging, and firefighting foam. Their carbon-fluorine bonds make them incredibly
                  resistant to breakdown.
                </p>
              </div>
            </CardContent>
          </Card>

          <!-- Medium Feature Cards -->
          <Card
            class="lg:col-span-2 bg-gradient-to-br from-secondary-50 to-white border-secondary-100 overflow-hidden"
          >
            <CardContent class="p-6">
              <div class="flex items-start space-x-4">
                <div
                  class="w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-md flex-shrink-0"
                >
                  <Globe class="w-6 h-6 text-secondary-600" />
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-slate-900 mb-2">Global Impact</h3>
                  <p class="text-sm text-slate-600">
                    PFAS contamination affects water sources, soil, and air quality worldwide, with
                    detection even in remote Arctic regions.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card class="bg-gradient-to-br from-red-50 to-white border-red-100 overflow-hidden">
            <CardContent class="p-6">
              <div class="flex items-start space-x-4">
                <div
                  class="w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-md flex-shrink-0"
                >
                  <AlertTriangle class="w-6 h-6 text-red-600" />
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-slate-900 mb-2">Health Risks</h3>
                  <p class="text-sm text-slate-600">
                    Linked to cancer, liver damage, decreased fertility, and increased cholesterol.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card class="bg-gradient-to-br from-green-50 to-white border-green-100 overflow-hidden">
            <CardContent class="p-6">
              <div class="flex items-start space-x-4">
                <div
                  class="w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-md flex-shrink-0"
                >
                  <Leaf class="w-6 h-6 text-green-600" />
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-slate-900 mb-2">Our Solution</h3>
                  <p class="text-sm text-slate-600">
                    Engineered bacteria that can break down PFAS molecules naturally and safely.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <!-- Stats Grid -->
        <div
          class="grid grid-cols-2 lg:grid-cols-4 gap-4 mt-8"
          data-aos="fade-up"
          data-aos-delay="200"
        >
          <div class="text-center p-6 bg-slate-50 rounded-2xl">
            <div class="text-3xl font-bold text-primary-600 mb-1">
              <AnimatedNumber :value="4000" suffix="+" :duration="2500" separator="," />
            </div>
            <div class="text-sm text-slate-600">PFAS Variants</div>
          </div>
          <div class="text-center p-6 bg-slate-50 rounded-2xl">
            <div class="text-3xl font-bold text-secondary-600 mb-1">
              <AnimatedNumber :value="1000" :duration="2000" separator="," />
            </div>
            <div class="text-sm text-slate-600">Years to Degrade</div>
          </div>
          <div class="text-center p-6 bg-slate-50 rounded-2xl">
            <div class="text-3xl font-bold text-red-600 mb-1">
              <AnimatedNumber :value="97" suffix="%" :duration="2000" />
            </div>
            <div class="text-sm text-slate-600">Population Exposed</div>
          </div>
          <div class="text-center p-6 bg-slate-50 rounded-2xl">
            <div class="text-3xl font-bold text-green-600 mb-1">
              <AnimatedNumber :value="100" suffix="%" :duration="2300" />
            </div>
            <div class="text-sm text-slate-600">Biological Solution</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Navigation Section with Modern Cards -->
    <section class="py-24 bg-gradient-to-b from-white to-slate-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16" data-aos="fade-up">
          <Badge variant="secondary" class="mb-4">
            <Compass class="w-3 h-3 mr-1" />
            Explore
          </Badge>
          <h2 class="text-4xl sm:text-5xl font-bold text-slate-900 mb-4">
            Dive Deeper Into Our Project
          </h2>
          <p class="text-xl text-slate-600 max-w-3xl mx-auto">
            Explore the different aspects of our iGEM journey and synthetic biology solution
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <!-- Project Overview Card -->
          <router-link
            to="/project/overview"
            class="group relative"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            <Card
              class="h-full bg-white hover:bg-gradient-to-br hover:from-primary-50 hover:to-white border-2 border-slate-200 hover:border-primary-400 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
            >
              <CardContent class="p-6 relative z-10">
                <div class="mb-4">
                  <div
                    class="w-12 h-12 bg-gradient-to-br from-primary-100 to-primary-200 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform"
                  >
                    <FileText class="w-6 h-6 text-primary-600" />
                  </div>
                </div>
                <h3
                  class="text-xl font-semibold text-slate-900 mb-2 group-hover:text-primary-700 transition-colors"
                >
                  Project Overview
                </h3>
                <p class="text-slate-600 text-sm mb-4">
                  Discover our innovative approach to breaking down PFAS using engineered bacteria
                </p>
                <div class="flex items-center text-primary-600 text-sm font-medium">
                  <span>Learn more</span>
                  <ArrowRight class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                </div>
              </CardContent>
            </Card>
          </router-link>

          <!-- Human Practices Card -->
          <router-link
            to="/human-practices"
            class="group relative"
            data-aos="fade-up"
            data-aos-delay="200"
          >
            <Card
              class="h-full bg-white hover:bg-gradient-to-br hover:from-secondary-50 hover:to-white border-2 border-slate-200 hover:border-secondary-400 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
            >
              <CardContent class="p-6 relative z-10">
                <div class="mb-4">
                  <div
                    class="w-12 h-12 bg-gradient-to-br from-secondary-100 to-secondary-200 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform"
                  >
                    <Users class="w-6 h-6 text-secondary-600" />
                  </div>
                </div>
                <h3
                  class="text-xl font-semibold text-slate-900 mb-2 group-hover:text-secondary-700 transition-colors"
                >
                  Human Practices
                </h3>
                <p class="text-slate-600 text-sm mb-4">
                  Engaging with communities and stakeholders to create real-world impact
                </p>
                <div class="flex items-center text-secondary-600 text-sm font-medium">
                  <span>Explore impact</span>
                  <ArrowRight class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                </div>
              </CardContent>
            </Card>
          </router-link>

          <!-- Team Card -->
          <router-link
            to="/teammembers"
            class="group relative"
            data-aos="fade-up"
            data-aos-delay="300"
          >
            <Card
              class="h-full bg-white hover:bg-gradient-to-br hover:from-green-50 hover:to-white border-2 border-slate-200 hover:border-green-400 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
            >
              <CardContent class="p-6 relative z-10">
                <div class="mb-4">
                  <div
                    class="w-12 h-12 bg-gradient-to-br from-green-100 to-green-200 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform"
                  >
                    <UserCheck class="w-6 h-6 text-green-600" />
                  </div>
                </div>
                <h3
                  class="text-xl font-semibold text-slate-900 mb-2 group-hover:text-green-700 transition-colors"
                >
                  Our Team
                </h3>
                <p class="text-slate-600 text-sm mb-4">
                  Meet the brilliant minds behind the SnaPFAS project
                </p>
                <div class="flex items-center text-green-600 text-sm font-medium">
                  <span>Meet the team</span>
                  <ArrowRight class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                </div>
              </CardContent>
            </Card>
          </router-link>

          <!-- Attribution Card -->
          <router-link
            to="/attribution"
            class="group relative"
            data-aos="fade-up"
            data-aos-delay="400"
          >
            <Card
              class="h-full bg-white hover:bg-gradient-to-br hover:from-purple-50 hover:to-white border-2 border-slate-200 hover:border-purple-400 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
            >
              <CardContent class="p-6 relative z-10">
                <div class="mb-4">
                  <div
                    class="w-12 h-12 bg-gradient-to-br from-purple-100 to-purple-200 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform"
                  >
                    <Award class="w-6 h-6 text-purple-600" />
                  </div>
                </div>
                <h3
                  class="text-xl font-semibold text-slate-900 mb-2 group-hover:text-purple-700 transition-colors"
                >
                  Attribution
                </h3>
                <p class="text-slate-600 text-sm mb-4">
                  Acknowledging contributions and support from our community
                </p>
                <div class="flex items-center text-purple-600 text-sm font-medium">
                  <span>View credits</span>
                  <ArrowRight class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                </div>
              </CardContent>
            </Card>
          </router-link>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup name="HomeView">
import AnimatedNumber from '@/components/ui/AnimatedNumber.vue'
import Badge from '@/components/ui/Badge.vue'
import Button from '@/components/ui/Button.vue'
import Card from '@/components/ui/Card.vue'
import CardContent from '@/components/ui/CardContent.vue'
import AOS from 'aos'
import 'aos/dist/aos.css'
import {
  AlertTriangle,
  ArrowRight,
  Award,
  Beaker,
  ChevronDown,
  Compass,
  FileText,
  FlaskRound,
  Globe,
  Leaf,
  Microscope,
  Sparkles,
  UserCheck,
  Users,
} from 'lucide-vue-next'
import { onMounted, ref } from 'vue'

const heroSection = ref(null)

onMounted(() => {
  AOS.init({
    duration: 1000,
    easing: 'ease-out-cubic',
    once: true,
    offset: 50,
    delay: 50,
  })
})
</script>

<style scoped>
/* Grid pattern background */
.bg-grid-pattern {
  background-image:
    linear-gradient(to right, rgba(0, 0, 0, 0.1) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
  background-size: 40px 40px;
}

/* Modern gradient animations */
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  animation: gradient 15s ease infinite;
  background-size: 400% 400%;
}

/* Floating animation for 3D orbs */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-30px);
  }
  100% {
    transform: translateY(0px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float 8s ease-in-out 2s infinite;
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Enhanced focus states for accessibility */
a:focus-visible,
button:focus-visible {
  outline: 2px solid var(--color-primary-400);
  outline-offset: 2px;
}

/* Prevent horizontal overflow */
section {
  overflow-x: hidden;
}

/* Responsive hero section */
@media (max-width: 768px) {
  .min-h-screen {
    min-height: 100vh;
    padding-top: 4rem;
  }
}

/* Card hover effects */
.group:hover .group-hover\:scale-110 {
  transform: scale(1.1);
}

.group:hover .group-hover\:translate-x-1 {
  transform: translateX(0.25rem);
}

.group:hover .group-hover\:translate-y-1 {
  transform: translateY(0.25rem);
}

/* Gradient border effect for cards */
.gradient-border {
  position: relative;
  background:
    linear-gradient(white, white) padding-box,
    linear-gradient(to right, var(--color-primary-600), var(--color-secondary-600)) border-box;
  border: 1px solid transparent;
}

/* AOS custom animations */
[data-aos='fade-up'] {
  transform: translateY(30px);
  opacity: 0;
  transition-property: transform, opacity;
  &.aos-animate {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Custom scrollbar for better UX */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: var(--color-primary-400);
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary-600);
}
</style>
